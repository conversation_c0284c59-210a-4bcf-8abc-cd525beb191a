import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  <PERSON>ginationFirst,
  PaginationItem,
  PaginationLast,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { type PageSizeOption } from "@/hooks/usePagination";
import { cn } from "@/lib/utils";
import { PageSizeSelector } from "./page-size-selector";

interface DataPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
  // Page size selector props
  pageSize?: PageSizeOption;
  onPageSizeChange?: (size: PageSizeOption) => void;
  totalCount?: number;
  showPageSizeSelector?: boolean;
}

export function DataPagination({
  currentPage,
  totalPages,
  onPageChange,
  className,
  showFirstLast = true,
  maxVisiblePages = 5,
  pageSize,
  onPageSizeChange,
  totalCount,
  showPageSizeSelector = false,
}: DataPaginationProps) {
  // Don't render if there's only one page or no pages
  if (totalPages <= 1) {
    return null;
  }

  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  // Calculate which page numbers to show
  const getVisiblePages = () => {
    const pages: (number | "ellipsis")[] = [];

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate start and end of visible range
      let start = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
      let end = Math.min(totalPages - 1, start + maxVisiblePages - 3);

      // Adjust start if end is at the limit
      if (end === totalPages - 1) {
        start = Math.max(2, end - maxVisiblePages + 3);
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pages.push("ellipsis");
      }

      // Add visible pages
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pages.push("ellipsis");
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className={cn("flex flex-col sm:flex-row items-center justify-between gap-4", className)}>
      {/* Page Size Selector */}
      {showPageSizeSelector && pageSize && onPageSizeChange && totalCount !== undefined && (
        <PageSizeSelector
          pageSize={pageSize}
          onPageSizeChange={onPageSizeChange}
          totalCount={totalCount}
          size="sm"
        />
      )}

      {/* Pagination Controls */}
      <div className="flex justify-center">
        <Pagination>
          <PaginationContent>
          {/* First page button */}
          {showFirstLast && (
            <PaginationItem>
              <PaginationFirst
                onClick={() => onPageChange(1)}
                className={
                  !hasPrevPage
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer hover:bg-accent hover:text-accent-foreground"
                }
              />
            </PaginationItem>
          )}

          {/* Previous page button */}
          <PaginationItem>
            <PaginationPrevious
              onClick={() => hasPrevPage && onPageChange(currentPage - 1)}
              className={
                !hasPrevPage
                  ? "pointer-events-none opacity-50"
                  : "cursor-pointer hover:bg-accent hover:text-accent-foreground"
              }
            />
          </PaginationItem>

          {/* Page numbers */}
          {visiblePages.map((page, index) => (
            <PaginationItem key={index}>
              {page === "ellipsis" ? (
                <PaginationEllipsis />
              ) : (
                <PaginationLink
                  onClick={() => onPageChange(page)}
                  isActive={currentPage === page}
                  className="cursor-pointer"
                >
                  {page}
                </PaginationLink>
              )}
            </PaginationItem>
          ))}

          {/* Next page button */}
          <PaginationItem>
            <PaginationNext
              onClick={() => hasNextPage && onPageChange(currentPage + 1)}
              className={
                !hasNextPage
                  ? "pointer-events-none opacity-50"
                  : "cursor-pointer hover:bg-accent hover:text-accent-foreground"
              }
            />
          </PaginationItem>

          {/* Last page button */}
          {showFirstLast && (
            <PaginationItem>
              <PaginationLast
                onClick={() => onPageChange(totalPages)}
                className={
                  !hasNextPage
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer hover:bg-accent hover:text-accent-foreground"
                }
              />
            </PaginationItem>
          )}
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
}
